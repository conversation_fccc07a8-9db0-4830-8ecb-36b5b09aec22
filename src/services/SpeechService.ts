import axios from "axios";
import { log } from "./LogService";
import { handleRequest } from "./_helpersService";
import type { CacheEntry } from "../models/speech";

// ========== INTERFACES ==========
interface IVoicesService {
  getAvailableVoices(): Promise<string[]>;
  getAudio(text: string): Promise<Blob>;
  configVoice(genre: string): Promise<boolean>;
  getCurrentVoiceId(): string;
  getAvailableVoicesList(): string[];
  reset(): void;
  cleanupCache(): void;
  getPerformanceStats(): object;
}

// ========== CONFIGURACIÓN API ==========
const LANGUAGE = "es";
const BASE_URL = import.meta.env.VITE_SPEECH_API_URL;
const API_KEY = import.meta.env.VITE_SPEECH_API_KEY;

// ========== AZURE VOICES SERVICE ==========
class AzureVoicesService implements IVoicesService {
  private static instance: AzureVoicesService;
  // ===== CORE =====
  private voiceId: string = "";
  private genre: string = "";
  private availableVoicesList: string[] = [];
  // ===== CACHE SYSTEM =====
  private audioCache = new Map<string, CacheEntry>();
  private readonly CACHE_TTL = 24 * 60 * 60 * 1000;
  // ===== RETRY QUEUE SYSTEM =====
  private retryQueue: Array<() => Promise<any>> = [];
  private isProcessingQueue = false;

  private constructor() {}

  public static getInstance(): AzureVoicesService {
    if (!AzureVoicesService.instance) {
      AzureVoicesService.instance = new AzureVoicesService();
    }
    return AzureVoicesService.instance;
  }

  // ========== GETTERS Y SETTERS ==========
  public getCurrentVoiceId(): string {
    return this.voiceId;
  }

  public getAvailableVoicesList(): string[] {
    return this.availableVoicesList;
  }

  private setVoiceId(voice: string) {
    this.voiceId = voice;
  }

  private setGenre(genre: string) {
    this.genre = genre;
  }

  // ========== CONFIGURACIÓN Y ESTADO ==========
  public reset(): void {
    log.info("AzureVoicesService", "🔄 Reseteando servicio...");

    // Core
    this.voiceId = "";
    this.genre = "";
    this.availableVoicesList = [];

    // Cache
    this.audioCache.clear();
    this.retryQueue = [];
    this.isProcessingQueue = false;
  }

  // ========== API DE AZURE ==========
  private getHeaders(): Record<string, string> {
    if (!API_KEY) {
      throw new Error("VITE_SPEECH_API_KEY no está configurada");
    }
    return {
      Authorization: `Bearer ${API_KEY}`,
      "Content-Type": "application/json",
    };
  }

  getAvailableVoices(): Promise<string[]> {
    if (!BASE_URL) {
      throw new Error("VITE_SPEECH_API_URL no está configurada");
    }

    const data = {
      language: LANGUAGE,
      gender: this.genre,
    };

    return handleRequest<string[]>(
      axios.post(`${BASE_URL}available_voices`, data, {
        headers: this.getHeaders(),
      }),
    );
  }

  public async configVoice(genre: string): Promise<boolean> {
    this.setGenre(genre);

     try {
      const voices = await this.getAvailableVoices();
      this.availableVoicesList = voices;

      if (voices.length > 0) {
        const randomVoice = voices[Math.floor(Math.random() * voices.length)];
        this.setVoiceId(randomVoice);
        log.success("AzureVoicesService", `Voz configurada: ${randomVoice}`);
        return true;
      }

      log.warn("AzureVoicesService", "No hay voces disponibles");
      return false;
    } catch (error) {
      log.error("AzureVoicesService", "Error configurando voz", error);
      this.availableVoicesList = [];
      return false;
    }
  }

  // ========== SISTEMA DE CACHE ==========
  private getCacheKey(text: string, voiceId: string): string {
    return `${voiceId}-${text.toLowerCase().trim()}`;
  }

  private getCachedAudio(text: string): Blob | null {
    const key = this.getCacheKey(text, this.voiceId);
    const cached = this.audioCache.get(key);

    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      console.log("🎯 Audio cache hit:", text.substring(0, 30));
      return cached.blob;
    }

    // Limpiar entrada expirada
    if (cached) {
      this.audioCache.delete(key);
    }

    return null;
  }

  private setCachedAudio(text: string, blob: Blob): void {
    const key = this.getCacheKey(text, this.voiceId);
    this.audioCache.set(key, {
      blob,
      timestamp: Date.now(),
      voiceId: this.voiceId
    });
  }

  public cleanupCache(): void {
    const now = Date.now();
    let removed = 0;

    for (const [key, entry] of this.audioCache.entries()) {
      if (now - entry.timestamp > this.CACHE_TTL) {
        this.audioCache.delete(key);
        removed++;
      }
    }

    if (removed > 0) {
      log.info("AzureVoicesService", `🧹 Cache limpiado: ${removed} entradas removidas`);
    }
  }

  // ========== SISTEMA DE RETRY QUEUE ==========
  private async handleThrottling<T>(request: () => Promise<T>): Promise<T> {
    try {
      return await request();
    } catch (error: any) {
      if (error.response?.status === 429) {
        log.warn("AzureVoicesService", "⚠️ Azure throttling detectado, encolando petición");
        return this.queueRequest(request);
      }
      throw error;
    }
  }

  private async queueRequest<T>(request: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.retryQueue.push(async () => {
        try {
          const result = await request();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      this.processQueue();
    });
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.retryQueue.length === 0) return;

    this.isProcessingQueue = true;
    log.info("AzureVoicesService", `🔄 Procesando cola de ${this.retryQueue.length} peticiones`);

    while (this.retryQueue.length > 0) {
      const request = this.retryQueue.shift()!;

      try {
        await request();
        // Esperar entre requests para evitar throttling
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        log.error("AzureVoicesService", "❌ Retry falló", error);
      }
    }

    this.isProcessingQueue = false;
    log.success("AzureVoicesService", "✅ Cola procesada completamente");
  }

  // ========== PROCESAMIENTO DE TEXTO ==========
  private optimizeTextForSpeech(text: string): string {
    return text
      .replace(/([.!?])\s+/g, '$1 <break time="500ms"/> ')
      .replace(/\b(Sí|Si)\b/g, '<emphasis level="moderate">Sí</emphasis>')
      .replace(/\b(No)\b/g, '<emphasis level="moderate">No</emphasis>')
      .replace(/\b(Tal vez|Quizás)\b/g, '<emphasis level="moderate">$1</emphasis>')
      .trim();
  }

  private wrapInSSML(text: string): string {
    return `
    <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="es-ES">
      <voice name="${this.voiceId}">
        <prosody rate="1.1" pitch="default">
          ${text}
        </prosody>
      </voice>
    </speak>`.trim();
  }

  // ========== MAIN ==========
  async getAudio(text: string): Promise<Blob> {
    if (!BASE_URL || !this.voiceId) {
      throw new Error("Azure Speech no configurado correctamente");
    }

    // Verificar cache primero
    const cachedAudio = this.getCachedAudio(text);
    if (cachedAudio) {
      return cachedAudio;
    }

    // Optimización de texto
    const optimizedText = this.optimizeTextForSpeech(text);

    return this.handleThrottling(async () => {
      const response = await handleRequest<Blob>(
        axios.post(`${BASE_URL}t2s`, {
          input_text: optimizedText,
          voice_params: {
            voice_id: this.voiceId,
            rate: 1.1,
            pitch: "default",
            volume: "default",
            style: "general"
          },
          output_format: "mp3",
          ssml: this.wrapInSSML(optimizedText)
        }, {
          responseType: "blob",
          timeout: 30000,
          headers: this.getHeaders()
        })
      );

      // Guardar en cache
      this.setCachedAudio(text, response);

      return response;
    });
  }

  // ========== UTILIDADES ==========
  public getPerformanceStats(): object {
    return {
      cache: {
        size: this.audioCache.size,
        hitRate: this.calculateCacheHitRate(),
        ttl: this.CACHE_TTL
      },
      queue: {
        length: this.retryQueue.length,
        isProcessing: this.isProcessingQueue
      },
      voice: {
        configured: Boolean(this.voiceId),
        voiceId: this.voiceId,
        availableCount: this.availableVoicesList.length
      }
    };
  }

  private calculateCacheHitRate(): number {
    // TODO: Implementar tracking real de hits/misses
    return 0;
  }
}

// ========== SPEECH SERVICE PRINCIPAL ==========
class SpeechService {
  private azureService: IVoicesService;
  private audioElement: HTMLAudioElement | null = null;
  private serviceName = "speechService";
  private userHasInteracted = false;

  // ========== CONSTRUCTOR E INICIALIZACIÓN ==========
  constructor() {
    this.azureService = AzureVoicesService.getInstance();
    this.initializeAudioElement();
    this.setupUserInteractionDetection();
  }

  private initializeAudioElement() {
    this.audioElement = document.getElementById("audio") as HTMLAudioElement;

    if (!this.audioElement) {
      this.audioElement = document.createElement("audio");
      this.audioElement.id = "audio";
      this.audioElement.preload = "metadata";
      document.body.appendChild(this.audioElement);
    }

    log.debug(this.serviceName, "🔊 Elemento de audio inicializado");
  }

  private setupUserInteractionDetection() {
    // Detectar primera interacción del usuario
    const events = ['click', 'touchstart', 'keydown'];

    const handleFirstInteraction = () => {
      this.userHasInteracted = true;
      log.info(this.serviceName, "👆 Primera interacción del usuario detectada");

      // Remover listeners después de la primera interacción
      events.forEach(event => {
        document.removeEventListener(event, handleFirstInteraction);
      });
    };

    events.forEach(event => {
      document.addEventListener(event, handleFirstInteraction, { once: true });
    });
  }

  // ========== GETTERS Y ESTADO ==========
  private getAudioElement(): HTMLAudioElement {
    if (!this.audioElement) {
      this.initializeAudioElement();
    }
    return this.audioElement!;
  }

  public getCurrentVoiceId(): string {
    return this.azureService.getCurrentVoiceId();
  }

  public getAvailableVoicesList(): string[] {
    return this.azureService.getAvailableVoicesList();
  }

  // ========== CONFIGURACIÓN DE VOZ ==========
  public async configVoice(genre: string): Promise<boolean> {
    try {
      log.info(this.serviceName, `🔧 Configurando voz: ${genre}`);
      const success = await this.azureService.configVoice(genre);

      if (success) {
        const voiceId = this.azureService.getCurrentVoiceId();
        log.success(this.serviceName, `✅ Voz configurada: ${voiceId}`);
      } else {
        log.warn(this.serviceName, "❌ No se pudo configurar la voz");
      }

      return success;
    } catch (error) {
      log.error(this.serviceName, "Error configurando voz", error);
      return false;
    }
  }

  // ========== API PRINCIPAL ==========
  public async getAudio(message: string): Promise<Blob> {
    try {
      return await this.azureService.getAudio(message);
    } catch (error) {
      log.error(this.serviceName, "Error obteniendo audio", error);
      throw error;
    }
  }

  public async getSpeech(message: string): Promise<string> {
    try {
      const audioBlob = await this.getAudio(message);
      const audioUrl = URL.createObjectURL(audioBlob);
      return audioUrl;
    } catch (error) {
      log.error(this.serviceName, "Error obteniendo URL de speech", error);
      throw error;
    }
  }

  // ========== CONTROL DE REPRODUCCIÓN ==========
  public async playSpeech(): Promise<void> {
    const audioElement = this.getAudioElement();
    audioElement.volume = 1;

    try {
      await audioElement.play();
    } catch (error) {
      if (error instanceof Error && error.name === 'NotAllowedError') {
        this.handleAutoplayBlocked();
      } else {
        throw error;
      }
    }
  }

  public stopSpeech(): void {
    const audioElement = this.getAudioElement();
    audioElement.volume = 0;
    audioElement.pause();
  }

  public noSpeech(): void {
    this.stopSpeech();
    const audioElement = this.getAudioElement();
    audioElement.onended = null;
    audioElement.src = "";
  }

  // ========== MANEJO DE AUTOPLAY ==========
  private handleAutoplayBlocked(): void {
    if (!this.userHasInteracted) {
      this.showAudioPermissionNotification();
      this.setupRetryOnInteraction();
    }
  }

  private setupRetryOnInteraction(): void {
    const playOnInteraction = async () => {
      try {
        const audioElement = this.getAudioElement();
        if (audioElement.src && audioElement.readyState >= 2) {
          await audioElement.play();
          log.success(this.serviceName, "✅ Audio reproducido tras interacción");
        }
      } catch (error) {
        log.error(this.serviceName, "Error reproduciendo audio tras interacción", error);
      }
    };

    const events = ['click', 'touchstart', 'keydown'];
    events.forEach(event => {
      document.addEventListener(event, playOnInteraction, { once: true });
    });
  }

  private showAudioPermissionNotification(): void {
    if (import.meta.env.MODE === "development") {
      log.info(this.serviceName, "🔔 Esperando interacción del usuario para reproducir audio");
    }
  }

  // ========== MÉTODOS DE CONVENIENCIA ==========
  public setSpeech(url: string): void {
    const audioElement = this.getAudioElement();
    audioElement.onended = null;
    audioElement.src = url;
    audioElement.currentTime = 0;
    audioElement.volume = 1;
  }

  public async toSpeech(url: string): Promise<void> {
    const audioElement = this.getAudioElement();
    audioElement.onended = null;
    audioElement.src = url;
    audioElement.currentTime = 0;
    audioElement.volume = 1;

    try {
      await audioElement.play();
    } catch (error) {
      if (error instanceof Error && error.name === 'NotAllowedError') {
        this.handleAutoplayBlocked();
      } else {
        throw error;
      }
    }
  }

  public async speak(text: string): Promise<void> {
    try {
      const audioUrl = await this.getSpeech(text);
      await this.toSpeech(audioUrl);
    } catch (error) {
      log.error(this.serviceName, "Error en speak", error);
      throw error;
    }
  }

  // ========== AUTO-CONFIGURACIÓN ==========
  public async speakWithAutoConfig(text: string, genre: string = "female"): Promise<void> {
    try {
      // Auto-configurar si no está configurado
      if (!this.getCurrentVoiceId()) {
        log.info(this.serviceName, "🔧 Auto-configurando voz...");
        const configured = await this.configVoice(genre);
        if (!configured) {
          throw new Error("No se pudo configurar la voz automáticamente");
        }
      }

      await this.speak(text);
    } catch (error) {
      log.error(this.serviceName, "Error en speakWithAutoConfig", error);
      throw error;
    }
  }








  // ========== MÉTODO DE AUTO-CONFIGURACIÓN ==========
  async speakWithAutoConfig(
    text: string,
    genre: string = "female",
  ): Promise<void> {
    try {
      // Verificar si ya está configurado
      if (!this.getCurrentVoiceId()) {
        console.log("🔧 SpeechService: Auto-configurando voz...");
        const configured = await this.configVoice(genre);
        if (!configured) {
          throw new Error("No se pudo configurar la voz automáticamente");
        }
      }

      await this.speak(text);
    } catch (error) {
      console.error("❌ SpeechService: Error en speakWithAutoConfig():", error);
      throw error;
    }
  }
}

// ========== INSTANCIA SINGLETON ==========
export const speechService = new SpeechService();
